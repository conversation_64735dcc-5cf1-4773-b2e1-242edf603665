#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动数据验证程序
逐步分析数据，找出筛选逻辑问题
"""

import pandas as pd
from datetime import datetime

def main():
    print("=" * 80)
    print("手动数据验证分析")
    print("=" * 80)
    
    try:
        # 加载数据
        df = pd.read_csv("merged_data_20250122_20250724.csv", encoding='utf-8')
        print(f"原始数据加载成功：{len(df)} 条记录")
        
        # 资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        print(f"\n检查列是否存在:")
        for col in fund_flow_columns + ['收益率']:
            exists = col in df.columns
            print(f"{'✓' if exists else '✗'} {col}")
        
        # 数据类型转换
        print(f"\n数据类型转换...")
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        # 检查空值
        print(f"\n空值检查:")
        for col in fund_flow_columns + ['收益率']:
            null_count = df[col].isnull().sum()
            print(f"{col}: {null_count} 个空值")
        
        # 删除空值
        df_clean = df.dropna(subset=fund_flow_columns + ['收益率'])
        print(f"\n数据清洗结果:")
        print(f"原始记录: {len(df)} 条")
        print(f"清洗后记录: {len(df_clean)} 条")
        print(f"删除记录: {len(df) - len(df_clean)} 条")
        
        # 验证条件1：主力净流入 > 10
        print(f"\n" + "=" * 60)
        print("验证条件1：主力净流入净占比 > 10")
        print("=" * 60)
        
        main_flow_col = 'FundFlow_主力净流入-净占比'
        condition1_mask = df_clean[main_flow_col] > 10
        condition1_count = condition1_mask.sum()
        
        print(f"主力净流入净占比统计:")
        print(f"  最小值: {df_clean[main_flow_col].min():.2f}")
        print(f"  最大值: {df_clean[main_flow_col].max():.2f}")
        print(f"  平均值: {df_clean[main_flow_col].mean():.2f}")
        print(f"  中位数: {df_clean[main_flow_col].median():.2f}")
        print(f"  > 10的记录数: {condition1_count}")
        
        # 显示一些符合条件的记录
        if condition1_count > 0:
            condition1_df = df_clean[condition1_mask]
            print(f"\n符合条件的前10条记录的主力净流入净占比:")
            print(condition1_df[main_flow_col].head(10).tolist())
            
            # 保存符合条件1的记录
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file1 = f'验证结果_主力净流入大于10_{timestamp}.csv'
            condition1_df.to_csv(output_file1, index=False, encoding='utf-8-sig')
            print(f"符合条件1的记录已保存到: {output_file1}")
        
        # 验证条件2：所有指标为正
        print(f"\n" + "=" * 60)
        print("验证条件2：所有5个资金流向指标均为正值")
        print("=" * 60)
        
        # 检查每个指标的正值统计
        print(f"各指标正值统计:")
        for col in fund_flow_columns:
            positive_count = (df_clean[col] > 0).sum()
            negative_count = (df_clean[col] < 0).sum()
            zero_count = (df_clean[col] == 0).sum()
            print(f"{col}:")
            print(f"  正值: {positive_count} 条 ({positive_count/len(df_clean)*100:.1f}%)")
            print(f"  负值: {negative_count} 条 ({negative_count/len(df_clean)*100:.1f}%)")
            print(f"  零值: {zero_count} 条 ({zero_count/len(df_clean)*100:.1f}%)")
        
        # 检查同时为正值的指标数量分布
        positive_counts = (df_clean[fund_flow_columns] > 0).sum(axis=1)
        print(f"\n同时为正值的指标数量分布:")
        for i in range(6):
            count = (positive_counts == i).sum()
            print(f"恰好{i}个指标为正: {count} 条 ({count/len(df_clean)*100:.1f}%)")
        
        print(f"\n至少N个指标为正的统计:")
        for i in range(1, 6):
            count = (positive_counts >= i).sum()
            print(f"至少{i}个指标为正: {count} 条 ({count/len(df_clean)*100:.1f}%)")
        
        # 所有指标均为正值
        condition2_mask = (df_clean[fund_flow_columns] > 0).all(axis=1)
        condition2_count = condition2_mask.sum()
        print(f"\n所有5个指标均为正值: {condition2_count} 条")
        
        if condition2_count > 0:
            condition2_df = df_clean[condition2_mask]
            print(f"符合条件的记录示例:")
            print(condition2_df[fund_flow_columns].head())
            
            # 保存符合条件2的记录
            output_file2 = f'验证结果_所有指标为正_{timestamp}.csv'
            condition2_df.to_csv(output_file2, index=False, encoding='utf-8-sig')
            print(f"符合条件2的记录已保存到: {output_file2}")
        else:
            print("确认：没有记录同时满足所有5个指标均为正值")
            
            # 找出最接近的情况（4个指标为正）
            condition_4pos_mask = positive_counts >= 4
            condition_4pos_count = condition_4pos_mask.sum()
            print(f"至少4个指标为正的记录: {condition_4pos_count} 条")
            
            if condition_4pos_count > 0:
                condition_4pos_df = df_clean[condition_4pos_mask]
                output_file2 = f'验证结果_至少4个指标为正_{timestamp}.csv'
                condition_4pos_df.to_csv(output_file2, index=False, encoding='utf-8-sig')
                print(f"至少4个指标为正的记录已保存到: {output_file2}")
        
        # 生成修正后的分析结果
        print(f"\n" + "=" * 80)
        print("修正后的分析结果")
        print("=" * 80)
        
        # 基准统计
        total_records = len(df_clean)
        positive_return_count = (df_clean['收益率'] > 0).sum()
        positive_return_pct = positive_return_count / total_records * 100
        gt1_return_count = (df_clean['收益率'] > 1).sum()
        gt1_return_pct = gt1_return_count / total_records * 100
        gt3_return_count = (df_clean['收益率'] > 3).sum()
        gt3_return_pct = gt3_return_count / total_records * 100
        
        print(f"基准统计（清洗后数据）:")
        print(f"总记录数: {total_records}")
        print(f"收益率>0%: {positive_return_count} 条 ({positive_return_pct:.2f}%)")
        print(f"收益率>1%: {gt1_return_count} 条 ({gt1_return_pct:.2f}%)")
        print(f"收益率>3%: {gt3_return_count} 条 ({gt3_return_pct:.2f}%)")
        
        # 修正后的筛选条件分析
        print(f"\n修正后的筛选条件分析:")
        print(f"1. 主力净流入净占比 > 10: {condition1_count} 条")
        print(f"2. 所有5个指标均为正值: {condition2_count} 条")
        print(f"3. 至少4个指标为正值: {condition_4pos_count} 条")
        print(f"4. 至少3个指标为正值: {(positive_counts >= 3).sum()} 条")
        
        print(f"\n问题分析:")
        print(f"1. 之前报告的数据与实际不符的原因:")
        print(f"   - 可能使用了错误的筛选逻辑或数据")
        print(f"   - 没有正确处理数据清洗过程")
        print(f"2. 实际数据特点:")
        print(f"   - 资金流向指标经常出现负值")
        print(f"   - 很少有股票同时满足所有指标为正值")
        print(f"   - 主力净流入>10%的情况相对较多")
        
        print(f"\n验证完成！")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
