#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证分析程序
重新检查原始数据，验证筛选逻辑的正确性
"""

import pandas as pd
from datetime import datetime

def main():
    print("=" * 80)
    print("数据验证分析 - 检查筛选逻辑")
    print("=" * 80)
    
    # 加载原始数据
    csv_file = "merged_data_20250122_20250724.csv"
    print(f"正在加载数据文件: {csv_file}")
    
    try:
        # 加载数据
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"原始数据加载成功：{len(df)} 条记录")
        
        # 显示数据基本信息
        print(f"\n数据基本信息:")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        
        # 显示前几行数据
        print(f"\n前5行数据:")
        print(df.head())
        
        # 检查资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        print(f"\n检查资金流向列:")
        for col in fund_flow_columns:
            if col in df.columns:
                print(f"✓ {col}")
            else:
                print(f"✗ {col}")
        
        # 检查收益率列
        if '收益率' in df.columns:
            print("✓ 收益率")
        else:
            print("✗ 收益率")
        
        # 数据类型转换前的统计
        print(f"\n转换前数据统计:")
        print(f"主力净流入净占比列的数据类型: {df['FundFlow_主力净流入-净占比'].dtype}")
        print(f"主力净流入净占比的前10个值:")
        print(df['FundFlow_主力净流入-净占比'].head(10))
        
        # 检查空值
        print(f"\n空值检查:")
        for col in fund_flow_columns + ['收益率']:
            null_count = df[col].isnull().sum()
            print(f"{col}: {null_count} 个空值")
        
        # 数据类型转换
        print(f"\n开始数据类型转换...")
        original_count = len(df)
        
        # 转换数据类型
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        # 检查转换后的空值
        print(f"\n转换后空值检查:")
        for col in fund_flow_columns + ['收益率']:
            null_count = df[col].isnull().sum()
            print(f"{col}: {null_count} 个空值")
        
        # 删除空值前后对比
        df_clean = df.dropna(subset=fund_flow_columns + ['收益率'])
        cleaned_count = len(df_clean)
        print(f"\n数据清洗结果:")
        print(f"原始记录数: {original_count}")
        print(f"清洗后记录数: {cleaned_count}")
        print(f"删除记录数: {original_count - cleaned_count}")
        
        # 验证筛选条件1：主力净流入净占比 > 10
        print(f"\n" + "=" * 60)
        print("验证筛选条件1：主力净流入净占比 > 10")
        print("=" * 60)
        
        condition1_mask = df_clean['FundFlow_主力净流入-净占比'] > 10
        condition1_count = condition1_mask.sum()
        condition1_df = df_clean[condition1_mask]
        
        print(f"符合条件的记录数: {condition1_count}")
        print(f"主力净流入净占比的统计:")
        print(f"  最小值: {df_clean['FundFlow_主力净流入-净占比'].min():.2f}")
        print(f"  最大值: {df_clean['FundFlow_主力净流入-净占比'].max():.2f}")
        print(f"  平均值: {df_clean['FundFlow_主力净流入-净占比'].mean():.2f}")
        print(f"  >10的记录数: {condition1_count}")
        
        if condition1_count > 0:
            print(f"\n符合条件记录的主力净流入净占比范围:")
            print(f"  最小值: {condition1_df['FundFlow_主力净流入-净占比'].min():.2f}")
            print(f"  最大值: {condition1_df['FundFlow_主力净流入-净占比'].max():.2f}")
            print(f"  平均值: {condition1_df['FundFlow_主力净流入-净占比'].mean():.2f}")
        
        # 验证筛选条件2：所有5个指标均为正值
        print(f"\n" + "=" * 60)
        print("验证筛选条件2：所有5个资金流向指标均为正值")
        print("=" * 60)
        
        # 检查每个指标的正值数量
        for col in fund_flow_columns:
            positive_count = (df_clean[col] > 0).sum()
            print(f"{col} > 0: {positive_count} 条记录")
        
        # 所有指标均为正值
        condition2_mask = (df_clean[fund_flow_columns] > 0).all(axis=1)
        condition2_count = condition2_mask.sum()
        condition2_df = df_clean[condition2_mask]
        
        print(f"\n所有5个指标均为正值的记录数: {condition2_count}")
        
        if condition2_count > 0:
            print(f"符合条件的记录示例:")
            print(condition2_df[fund_flow_columns].head())
        else:
            print("没有记录同时满足所有5个指标均为正值")
            
            # 检查最接近的情况
            positive_counts = (df_clean[fund_flow_columns] > 0).sum(axis=1)
            max_positive = positive_counts.max()
            print(f"最多有 {max_positive} 个指标同时为正值")
            
            for i in range(5, 0, -1):
                count = (positive_counts >= i).sum()
                print(f"至少 {i} 个指标为正值: {count} 条记录")
        
        # 保存验证结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存条件1的结果
        if condition1_count > 0:
            output_file1 = f'验证结果_主力净流入大于10_{timestamp}.csv'
            condition1_df.to_csv(output_file1, index=False, encoding='utf-8-sig')
            print(f"\n条件1筛选结果已保存到: {output_file1}")
        
        # 保存条件2的结果
        if condition2_count > 0:
            output_file2 = f'验证结果_所有指标为正_{timestamp}.csv'
            condition2_df.to_csv(output_file2, index=False, encoding='utf-8-sig')
            print(f"条件2筛选结果已保存到: {output_file2}")
        else:
            # 保存至少4个指标为正的结果作为参考
            condition_4pos_mask = (df_clean[fund_flow_columns] > 0).sum(axis=1) >= 4
            condition_4pos_df = df_clean[condition_4pos_mask]
            if len(condition_4pos_df) > 0:
                output_file2 = f'验证结果_至少4个指标为正_{timestamp}.csv'
                condition_4pos_df.to_csv(output_file2, index=False, encoding='utf-8-sig')
                print(f"至少4个指标为正的结果已保存到: {output_file2}")
        
        # 生成详细的验证报告
        print(f"\n" + "=" * 80)
        print("问题分析和修正说明")
        print("=" * 80)
        
        print(f"1. 数据清洗影响:")
        print(f"   - 原始数据: {original_count} 条")
        print(f"   - 清洗后数据: {cleaned_count} 条")
        print(f"   - 这可能是造成记录数差异的主要原因")
        
        print(f"\n2. 主力净流入 > 10 的验证:")
        print(f"   - 实际符合条件: {condition1_count} 条")
        print(f"   - 之前报告的: 89 条")
        print(f"   - 差异原因: 可能是数据清洗或筛选逻辑问题")
        
        print(f"\n3. 所有指标为正值的验证:")
        print(f"   - 实际符合条件: {condition2_count} 条")
        print(f"   - 之前报告的: 45 条")
        if condition2_count == 0:
            print(f"   - 重要发现: 没有记录同时满足所有5个指标均为正值")
            print(f"   - 这说明之前的分析结果存在错误")
        
        print(f"\n验证完成！请检查生成的CSV文件以确认筛选结果。")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
