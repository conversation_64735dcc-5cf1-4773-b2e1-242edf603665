#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成验证文件 - 包含原始数据的关键列
"""

import pandas as pd
from datetime import datetime

def main():
    try:
        # 加载数据
        df = pd.read_csv("merged_data_20250122_20250724.csv", encoding='utf-8')
        print(f"加载数据: {len(df)} 条记录")
        
        # 资金流向列
        fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        
        # 数据类型转换
        for col in fund_flow_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')
        
        # 删除空值
        df_clean = df.dropna(subset=fund_flow_columns + ['收益率'])
        print(f"清洗后数据: {len(df_clean)} 条记录")
        
        # 添加辅助列
        df_clean['主力净流入大于10'] = df_clean['FundFlow_主力净流入-净占比'] > 10
        df_clean['主力净流入大于5'] = df_clean['FundFlow_主力净流入-净占比'] > 5
        df_clean['主力净流入大于0'] = df_clean['FundFlow_主力净流入-净占比'] > 0
        
        # 计算正值指标数量
        positive_counts = (df_clean[fund_flow_columns] > 0).sum(axis=1)
        df_clean['正值指标数量'] = positive_counts
        df_clean['所有指标为正'] = positive_counts == 5
        df_clean['至少4个指标为正'] = positive_counts >= 4
        df_clean['至少3个指标为正'] = positive_counts >= 3
        
        # 收益率分类
        df_clean['收益率大于0'] = df_clean['收益率'] > 0
        df_clean['收益率大于1'] = df_clean['收益率'] > 1
        df_clean['收益率大于3'] = df_clean['收益率'] > 3
        
        # 选择要保存的列
        output_columns = [
            '股票代码', '股票名称', '日期', '收益率',
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比',
            '正值指标数量',
            '主力净流入大于10', '主力净流入大于5', '主力净流入大于0',
            '所有指标为正', '至少4个指标为正', '至少3个指标为正',
            '收益率大于0', '收益率大于1', '收益率大于3'
        ]
        
        # 只保留存在的列
        available_columns = [col for col in output_columns if col in df_clean.columns]
        
        # 生成验证文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'验证数据_包含原始记录_{timestamp}.csv'
        
        df_clean[available_columns].to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"验证文件已生成: {output_file}")
        
        # 输出统计信息
        print(f"\n筛选条件统计:")
        print(f"主力净流入>10%: {df_clean['主力净流入大于10'].sum()} 条")
        print(f"主力净流入>5%: {df_clean['主力净流入大于5'].sum()} 条")
        print(f"主力净流入>0%: {df_clean['主力净流入大于0'].sum()} 条")
        print(f"所有指标为正: {df_clean['所有指标为正'].sum()} 条")
        print(f"至少4个指标为正: {df_clean['至少4个指标为正'].sum()} 条")
        print(f"至少3个指标为正: {df_clean['至少3个指标为正'].sum()} 条")
        
        # 生成主力净流入>10%的专门文件
        condition1_df = df_clean[df_clean['主力净流入大于10']]
        if len(condition1_df) > 0:
            output_file1 = f'验证_主力净流入大于10_{timestamp}.csv'
            condition1_df[available_columns].to_csv(output_file1, index=False, encoding='utf-8-sig')
            print(f"主力净流入>10%的记录已保存: {output_file1}")
        
        print("验证文件生成完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
