import os
import pandas as pd
from datetime import datetime, timedelta
import requests
import os

# 定义目录路径
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 定义日期范围
start_date = "20250122"
end_date = "20250724"
# end_date = (datetime.today() - timedelta(days=0)).strftime("%Y%m%d")
# start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=7)).strftime("%Y%m%d")
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\1zhang_ndie_' + end_date + '.csv'

# 获取所有交易日的文件名
all_trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) if f.endswith('.csv')])
# 过滤日期范围内的交易日
trading_dates = [date for date in all_trading_dates if start_date <= date <= end_date]

# 读取并合并数据
data = []
for date in trading_dates:
    equd_file = os.path.join(equd_dir, f"{date}.csv")
    limit_file = os.path.join(limit_dir, f"{date}.csv")
    mkt_stock_factors_file = os.path.join(mkt_stock_factors_dir, f"{date}.csv")

    if not os.path.exists(equd_file) or not os.path.exists(limit_file):
        continue  # 如果当天不是交易日，跳过

    equd_df = pd.read_csv(equd_file, encoding='gbk')
    limit_df = pd.read_csv(limit_file, encoding='gbk')
    mkt_stock_factors_df = pd.read_csv(mkt_stock_factors_file, encoding='gbk')

    merged_df = pd.merge(equd_df, limit_df, on=["secID", "secShortName"])
    merged_df = pd.merge(merged_df, mkt_stock_factors_df, on=["secID"])
    merged_df["date"] = date
    data.append(merged_df)

# 合并所有日期的数据
all_data = pd.concat(data)
# 过滤不需要的数据
all_data = all_data[(all_data['ticker_x'] < 700000) & 
        ((all_data['exchangeCD_x'] == "XSHE") | (all_data['exchangeCD_x'] == "XSHG")) & 
        (~all_data['secShortName'].str.contains('B')) & 
        (~all_data['secShortName'].str.contains('ST')) & (all_data['closePrice'] > 3)]

# 按secID和日期排序
all_data = all_data.sort_values(by=["secID", "date"])

# 找到符合条件的记录
results = []
total_groups = len(all_data['secID'].unique())
current_group = 0

for idx, (secID, group) in enumerate(all_data.groupby("secID")):
    print(f"Index proportion: {idx + 1} / {total_groups}, secID: {secID}")
    current_group += 1
    secShortName = group.iloc[0]["secShortName"]
    # print(f"Processing secID: {secID}, secShortName: {secShortName}, Progress: {current_group}/{total_groups}")
    group = group.reset_index(drop=True)
    i = 1  # 从第二天开始检查，因为需要检查前一天是否涨停
    while i < len(group) - 2:
        # 检查第1天的涨停，且前一天不是涨停
        if group.loc[i, "limitUpPrice"] == group.loc[i, "closePrice"] and group.loc[i-1, "limitUpPrice"] != group.loc[i-1, "closePrice"]:
            # 检查接下来2天的下跌情况
            if (group.loc[i+1, "chgPct"] <= 0) and (group.loc[i+2, "chgPct"] <= 0) and (group.loc[i, "KDJ_K"] > group.loc[i, "KDJ_D"]) and (group.loc[i+1, "KDJ_K"] > group.loc[i+1, "KDJ_D"]) and (group.loc[i+2, "KDJ_K"] > group.loc[i+2, "KDJ_D"]) and (group.loc[i, "MA5"] > group.loc[i, "MA10"]) and (group.loc[i, "MA10"] > group.loc[i, "MA20"]) and (group.loc[i+1, "MA5"] > group.loc[i+1, "MA10"]) and (group.loc[i+1, "MA10"] > group.loc[i+1, "MA20"]) and (group.loc[i+2, "MA5"] > group.loc[i+2, "MA10"]) and (group.loc[i+2, "MA10"] > group.loc[i+2, "MA20"]):
                chgPct_sum = group.loc[i+1:i+2, "chgPct"].sum()
                last_down_closePrice = group.loc[i+2, "closePrice"]
                # 确保访问 i+4 时不超出范围
                if i+4 < len(group):
                    highestPrice = group.loc[i+4, "highestPrice"]
                else:
                    highestPrice = 0
                # print(highestPrice)
                if i+3 < len(group):
                    One_openPrice = group.loc[i+3, "openPrice"]
                    One_lowestPrice = group.loc[i+3, "lowestPrice"]
                else:
                    One_openPrice = 0
                    One_lowestPrice = 0

                results.append({
                    "secID": secID,
                    "secShortName": group.loc[i, "secShortName"],
                    "start_up_date": group.loc[i, "date"],
                    "up_days": 1,  # 1天涨停
                    "start_down_date": group.loc[i+1, "date"],
                    "down_days": 2,  # 2天的下跌
                    "chgPct_sum": chgPct_sum,  # 下跌天数的chgPct之和
                    "start_up_openPrice": group.loc[i, "openPrice"],  # 开始上涨第一天的openPrice
                    "end_down_closePrice": last_down_closePrice,  # 下跌结束最后一天的closePrice
                    "1openPrice": One_openPrice,  # 下跌结束最后一天的closePrice
                    "1lowestPrice": One_lowestPrice,  # 下跌结束最后一天的closePrice
                    "2highestPrice": highestPrice  # 下跌结束第二天的highestPrice
                })
                # 更新i的值，跳过已处理的部分
                i += 2
            else:
                # 如果没有找到符合条件的下跌情况，i需要递增
                i += 1
        else:
            i += 1

# 只保留start_down_date最大的那天的数据
# if results:
#     max_start_down_date = max(result["start_down_date"] for result in results)
#     results = [result for result in results if result["start_down_date"] == max_start_down_date]

# 转换结果为DataFrame
results_df = pd.DataFrame(results)
results_df = results_df.sort_values(by='start_down_date', ascending=True)
results_df['down_open_Pct'] = round((results_df['end_down_closePrice']/results_df['start_up_openPrice']-1)*100, 2)
results_df['min_buy'] = results_df[['end_down_closePrice', '1openPrice']].min(axis=1)
results_df['2H_buy'] = round((results_df['2highestPrice']/results_df['min_buy']-1)*100, 2)
# 筛选出down_days+1大于等于up_days的内容
# filtered_results_df = results_df[results_df['down_days'] + 1 >= results_df['up_days']]

# 保存结果到CSV文件
results_df.to_csv(output_file, index=False, encoding='gbk')

print(f"结果已保存到 {output_file}")