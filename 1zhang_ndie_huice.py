#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
涨停后8日跟踪分析程序
功能：找出涨停股票，记录涨停当天及后续7个交易日的完整数据
特色：包含资金流向数据，支持完整的8日跟踪分析
作者：AI Assistant
创建时间：2025-07-26
更新时间：2025-07-26

核心筛选条件：
1. 找出第一天涨停的股票（收盘价等于涨停价）
2. 确保涨停前一个交易日不是涨停状态
3. 记录涨停当天及后续7个交易日的数据（共8天）

数据记录要求：
- 涨停当天（第1天）
- 涨停后第2个交易日到第8个交易日（共7天）
- 总计需要记录8个交易日的数据

每日记录字段：
1. openPrice - 开盘价
2. closePrice - 收盘价
3. chgPct - 涨跌幅
4. highestPrice - 最高价
5. 资金流向相关数据（净占比字段）
"""

import os
import pandas as pd
import csv
from datetime import datetime, timedelta

# 定义目录路径
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'
fund_flow_dir = r'stock_fund_flow_data'

# 定义日期范围
start_date = "20250122"
end_date = "20250724"
print("Start date:", start_date)
print("End date:", end_date)
output_file = f'd:\\Andy\\coding\\gupiao_huice\\zhangting_8days_analysis_{end_date}.csv'

# 辅助函数
def extract_stock_code(sec_id):
    """从SecID中提取6位股票代码"""
    if not sec_id or not isinstance(sec_id, str):
        return ""
    return sec_id[:6]

def find_matching_fund_flow_file(stock_code, fund_flow_dir):
    """查找匹配的资金流向文件"""
    patterns = [
        f"{stock_code}_sz_fund_flow.csv",  # 深圳交易所
        f"{stock_code}_sh_fund_flow.csv"   # 上海交易所
    ]

    for pattern in patterns:
        file_path = os.path.join(fund_flow_dir, pattern)
        if os.path.exists(file_path):
            return file_path
    return None

def standardize_date_format(date_str):
    """标准化日期格式为YYYY-MM-DD"""
    if not date_str:
        return ""

    try:
        # 处理YYYYMMDD格式
        if len(date_str) == 8 and date_str.isdigit():
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{year}-{month}-{day}"
        return date_str
    except Exception:
        return ""

def convert_date_to_yyyymmdd(date_str):
    """将YYYY-MM-DD格式转换为YYYYMMDD格式"""
    if not date_str:
        return ""
    try:
        return date_str.replace('-', '')
    except Exception:
        return ""

# 获取所有交易日的文件名
print("1. 获取交易日列表...")
all_trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) if f.endswith('.csv')])
# 过滤日期范围内的交易日
trading_dates = [date for date in all_trading_dates if start_date <= date <= end_date]
print(f"   找到 {len(trading_dates)} 个交易日")

# 读取并合并数据
print("2. 读取股票基础数据...")
data = []
for i, date in enumerate(trading_dates):
    if (i + 1) % 10 == 0 or i == 0:
        print(f"   处理进度: {i + 1}/{len(trading_dates)} ({date})")

    equd_file = os.path.join(equd_dir, f"{date}.csv")
    limit_file = os.path.join(limit_dir, f"{date}.csv")

    if not os.path.exists(equd_file) or not os.path.exists(limit_file):
        continue  # 如果当天不是交易日，跳过

    try:
        equd_df = pd.read_csv(equd_file, encoding='gbk')
        limit_df = pd.read_csv(limit_file, encoding='gbk')

        merged_df = pd.merge(equd_df, limit_df, on=["secID", "secShortName"])
        merged_df["date"] = date
        data.append(merged_df)
    except Exception as e:
        print(f"   警告：读取 {date} 数据失败: {str(e)}")
        continue

if not data:
    print("错误：没有读取到任何数据")
    exit(1)

# 合并所有日期的数据
print("3. 合并和过滤数据...")
all_data = pd.concat(data, ignore_index=True)
print(f"   原始数据记录数: {len(all_data)}")

# 过滤不需要的数据
all_data = all_data[(all_data['ticker_x'] < 700000) &
        ((all_data['exchangeCD_x'] == "XSHE") | (all_data['exchangeCD_x'] == "XSHG")) &
        (~all_data['secShortName'].str.contains('B')) &
        (~all_data['secShortName'].str.contains('ST')) & (all_data['closePrice'] > 3)]

print(f"   过滤后数据记录数: {len(all_data)}")

# 按secID和日期排序
all_data = all_data.sort_values(by=["secID", "date"])
print(f"   涉及股票数量: {all_data['secID'].nunique()}")

# 找到符合条件的涨停记录并记录8日数据
print("4. 识别涨停股票并记录8日数据...")
results = []
total_groups = len(all_data['secID'].unique())

for idx, (secID, group) in enumerate(all_data.groupby("secID")):
    if (idx + 1) % 100 == 0 or idx == 0:
        print(f"   处理进度: {idx + 1}/{total_groups}, secID: {secID}")

    secShortName = group.iloc[0]["secShortName"]
    group = group.reset_index(drop=True)

    # 从第二天开始检查，因为需要检查前一天是否涨停
    i = 1
    while i < len(group):
        # 检查涨停条件：当天涨停且前一天不是涨停
        if (group.loc[i, "limitUpPrice"] == group.loc[i, "closePrice"] and
            group.loc[i-1, "limitUpPrice"] != group.loc[i-1, "closePrice"]):

            # 确保有足够的后续交易日数据（至少需要7天）
            if i + 7 < len(group):
                # 记录涨停当天及后续7天的数据（共8天）
                zhangting_date = group.loc[i, "date"]
                stock_code = extract_stock_code(secID)

                # 查找资金流向文件
                fund_flow_file = find_matching_fund_flow_file(stock_code, fund_flow_dir)
                fund_flow_data = {}

                if fund_flow_file:
                    try:
                        fund_df = pd.read_csv(fund_flow_file, encoding='utf-8-sig')
                        # 将资金流向数据转换为字典，以日期为键
                        for _, row in fund_df.iterrows():
                            date_key = convert_date_to_yyyymmdd(row['日期'])
                            if date_key:
                                fund_flow_data[date_key] = row
                    except Exception as e:
                        print(f"   警告：读取资金流向文件失败 {fund_flow_file}: {str(e)}")

                # 记录8天的数据
                for day_offset in range(8):  # 0-7，共8天
                    day_index = i + day_offset
                    day_data = group.loc[day_index]
                    current_date = day_data["date"]

                    # 基础数据
                    record = {
                        "secID": secID,
                        "secShortName": secShortName,
                        "zhangting_date": zhangting_date,  # 涨停日期
                        "current_date": current_date,      # 当前记录日期
                        "day_offset": day_offset,          # 相对涨停日的偏移（0=涨停当天）
                        "openPrice": day_data["openPrice"],
                        "closePrice": day_data["closePrice"],
                        "chgPct": day_data["chgPct"],
                        "highestPrice": day_data["highestPrice"]
                    }

                    # 添加资金流向数据
                    if current_date in fund_flow_data:
                        fund_row = fund_flow_data[current_date]
                        record.update({
                            "主力净流入_净占比": fund_row.get("主力净流入-净占比", ""),
                            "超大单净流入_净占比": fund_row.get("超大单净流入-净占比", ""),
                            "大单净流入_净占比": fund_row.get("大单净流入-净占比", ""),
                            "中单净流入_净占比": fund_row.get("中单净流入-净占比", ""),
                            "小单净流入_净占比": fund_row.get("小单净流入-净占比", "")
                        })
                    else:
                        # 如果没有资金流向数据，填入空值
                        record.update({
                            "主力净流入_净占比": "",
                            "超大单净流入_净占比": "",
                            "大单净流入_净占比": "",
                            "中单净流入_净占比": "",
                            "小单净流入_净占比": ""
                        })

                    results.append(record)

                # 跳过已处理的8天，避免重复处理
                i += 8
            else:
                i += 1
        else:
            i += 1

# 保存结果
print("5. 保存分析结果...")
if not results:
    print("   警告：没有找到符合条件的涨停股票")
    exit(1)

# 转换结果为DataFrame
results_df = pd.DataFrame(results)
print(f"   找到 {len(results_df)} 条记录")
print(f"   涉及 {results_df['secID'].nunique()} 只涨停股票")

# 按涨停日期和股票代码排序
results_df = results_df.sort_values(by=['zhangting_date', 'secID', 'day_offset'])

# 保存结果到CSV文件
try:
    results_df.to_csv(output_file, index=False, encoding='gbk')
    print(f"   ✓ 结果已保存到: {output_file}")
except Exception as e:
    print(f"   错误：保存文件失败: {str(e)}")
    # 尝试使用utf-8编码保存
    try:
        output_file_utf8 = output_file.replace('.csv', '_utf8.csv')
        results_df.to_csv(output_file_utf8, index=False, encoding='utf-8-sig')
        print(f"   ✓ 已使用UTF-8编码保存到: {output_file_utf8}")
    except Exception as e2:
        print(f"   错误：UTF-8保存也失败: {str(e2)}")

# 显示统计信息
print("\n6. 数据统计报告")
print("="*60)
print(f"分析日期范围: {start_date} 到 {end_date}")
print(f"总交易日数: {len(trading_dates)}")
print(f"找到涨停股票数量: {results_df['secID'].nunique()}")
print(f"总记录数: {len(results_df)}")

# 按涨停日期统计
zhangting_stats = results_df[results_df['day_offset'] == 0].groupby('zhangting_date').size()
print(f"\n各日期涨停股票数量:")
for date, count in zhangting_stats.items():
    print(f"  {date}: {count}只")

# 显示数据示例
print(f"\n数据示例（前5行）:")
print("-" * 60)
if len(results_df) > 0:
    # 显示列名
    print("列名:")
    for i, col in enumerate(results_df.columns):
        print(f"  {i+1:2d}. {col}")

    print(f"\n前5行数据:")
    for i, (_, row) in enumerate(results_df.head().iterrows()):
        print(f"\n第 {i+1} 行:")
        print(f"  股票: {row['secID']} ({row['secShortName']})")
        print(f"  涨停日期: {row['zhangting_date']}")
        print(f"  当前日期: {row['current_date']} (第{row['day_offset']+1}天)")
        print(f"  开盘价: {row['openPrice']}, 收盘价: {row['closePrice']}")
        print(f"  涨跌幅: {row['chgPct']}%, 最高价: {row['highestPrice']}")
        print(f"  主力净占比: {row['主力净流入_净占比']}")

print("="*60)
print("程序执行完成！")

# 执行完成提示
print("\n正在执行完成提示...")
try:
    import subprocess
    subprocess.run([
        "powershell", "-Command",
        "Add-Type -AssemblyName PresentationFramework; [System.Windows.MessageBox]::Show('搞完了', 'Augment')"
    ], check=False)
except Exception:
    pass  # 如果执行失败，忽略错误