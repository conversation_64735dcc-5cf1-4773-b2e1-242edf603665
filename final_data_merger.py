#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版股票数据合并程序
功能：将主文件与资金流向数据按日期和股票代码进行合并
特色：支持合并涨停日期及前5个交易日的净占比数据
作者：AI Assistant
创建时间：2025-07-25
更新时间：2025-07-26

使用方法：
python final_data_merger.py

输入文件：
1. 主文件：1stzhangting_huice_kdj_20250601_20250630.csv
2. 数据源目录：stock_fund_flow_data/

功能特点：
- 对每只涨停股票，合并涨停当天及前5个交易日的资金流向数据
- 使用行索引方式获取前5个交易日，无需复杂日期计算
- 只保留"净占比"相关字段，精简数据结构
- 原始日期字段放在每个交易日数据的最前面
- 保留每条数据的原始日期标识

输出：
- 合并后的CSV文件，文件名包含时间戳
- 详细的处理统计报告，包含匹配天数统计
"""

import csv
import os
import glob
from datetime import datetime, timedelta

def extract_stock_code(sec_id):
    """从SecID中提取6位股票代码"""
    if not sec_id or not isinstance(sec_id, str):
        return ""
    return sec_id[:6]

def standardize_date_format(date_str):
    """标准化日期格式"""
    if not date_str:
        return ""
    
    try:
        date_str = str(date_str).strip()
        
        # 处理 "2025/6/18" 格式
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3:
                year, month, day = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        # 处理 "2025-06-18" 格式
        if '-' in date_str and len(date_str.split('-')) == 3:
            return date_str
        
        return ""
        
    except Exception as e:
        print(f"日期格式转换失败: {date_str}, 错误: {str(e)}")
        return ""

def clean_bom_from_text(text):
    """清理文本中的BOM字符"""
    if isinstance(text, str):
        return text.lstrip('\ufeff').strip()
    return text

def get_trading_days_before(target_date_str, days_count=5):
    """
    获取指定日期前N个交易日的日期列表

    Args:
        target_date_str: 目标日期字符串，格式为 "YYYY-MM-DD"
        days_count: 需要获取的交易日数量，默认5天

    Returns:
        list: 包含目标日期和前N个交易日的日期字符串列表，按时间倒序排列
    """
    try:
        # 解析目标日期
        target_date = datetime.strptime(target_date_str, "%Y-%m-%d")

        # 存储交易日期
        trading_dates = [target_date_str]  # 包含目标日期本身

        current_date = target_date
        found_days = 0

        # 向前查找交易日
        while found_days < days_count:
            current_date -= timedelta(days=1)

            # 跳过周末（周六=5, 周日=6）
            if current_date.weekday() < 5:  # 周一到周五
                trading_dates.append(current_date.strftime("%Y-%m-%d"))
                found_days += 1

        return trading_dates

    except Exception as e:
        print(f"计算交易日失败: {target_date_str}, 错误: {str(e)}")
        return [target_date_str]  # 出错时至少返回目标日期

def find_matching_file(stock_code, data_source_dir):
    """查找匹配的资金流向文件"""
    patterns = [
        f"{stock_code}_sz_fund_flow.csv",  # 深圳交易所
        f"{stock_code}_sh_fund_flow.csv"   # 上海交易所
    ]

    for pattern in patterns:
        file_path = os.path.join(data_source_dir, pattern)
        if os.path.exists(file_path):
            return file_path

    return None

def read_csv_file(file_path):
    """读取CSV文件，自动处理BOM问题"""
    try:
        # 首先尝试使用utf-8-sig编码，这会自动处理BOM
        with open(file_path, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            data = list(reader)
            # 清理可能残留的BOM字符
            if data:
                cleaned_data = []
                for row in data:
                    cleaned_row = {}
                    for key, value in row.items():
                        # 清理列名中的BOM字符
                        clean_key = key.lstrip('\ufeff').strip()
                        cleaned_row[clean_key] = value
                    cleaned_data.append(cleaned_row)
                return cleaned_data
            return data
    except UnicodeDecodeError:
        # 如果utf-8-sig失败，尝试其他编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'cp1252']
        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc, newline='') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)
                    # 清理BOM字符
                    if data:
                        cleaned_data = []
                        for row in data:
                            cleaned_row = {}
                            for key, value in row.items():
                                clean_key = key.lstrip('\ufeff').strip()
                                cleaned_row[clean_key] = value
                            cleaned_data.append(cleaned_row)
                        return cleaned_data
                    return data
            except (UnicodeDecodeError, Exception):
                continue

        print(f"读取文件失败 {file_path}: 无法识别文件编码")
        return []
    except Exception as e:
        print(f"读取文件失败 {file_path}: {str(e)}")
        return []

def write_csv_file(file_path, data, fieldnames):
    """写入CSV文件"""
    try:
        with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        return True
    except Exception as e:
        print(f"写入文件失败 {file_path}: {str(e)}")
        return False

def main():
    """主函数"""
    # 配置文件路径
    main_file = r"1stzhangting_huice_kdj_20250122_20250724.csv"
    data_source_dir = r"stock_fund_flow_data"
    
    print("="*60)
    print("股票数据合并程序（最终版）")
    print("="*60)
    
    # 验证输入文件和目录
    print("1. 验证输入文件和目录...")
    if not os.path.exists(main_file):
        print(f"错误：主文件不存在: {main_file}")
        return
    
    if not os.path.exists(data_source_dir):
        print(f"错误：数据源目录不存在: {data_source_dir}")
        return
    
    csv_files = glob.glob(os.path.join(data_source_dir, "*.csv"))
    if not csv_files:
        print(f"错误：数据源目录中没有找到CSV文件: {data_source_dir}")
        return
    
    print(f"   ✓ 主文件: {main_file}")
    print(f"   ✓ 数据源目录: {data_source_dir} (包含 {len(csv_files)} 个CSV文件)")
    
    # 加载主文件
    print("\n2. 加载主文件...")
    main_data = read_csv_file(main_file)
    if not main_data:
        print(f"错误：无法读取主文件: {main_file}")
        return
    
    print(f"   ✓ 主文件加载成功，共 {len(main_data)} 条记录")
    
    # 检查必要列（智能匹配，处理BOM问题）
    if not main_data:
        print("错误：主文件为空")
        return

    first_row = main_data[0]
    available_columns = list(first_row.keys())

    # 智能查找必要列（处理BOM和编码问题）
    def find_column(target_col, available_cols):
        """智能查找列名，处理BOM和编码问题"""
        # 直接匹配
        if target_col in available_cols:
            return target_col

        # 清理BOM后匹配
        for col in available_cols:
            clean_col = clean_bom_from_text(col)
            if clean_col == target_col:
                return col

        # 模糊匹配（包含目标字符串）
        for col in available_cols:
            clean_col = clean_bom_from_text(col)
            if target_col in clean_col or clean_col in target_col:
                return col

        return None

    # 查找secID列
    secid_column = find_column('secID', available_columns)
    if not secid_column:
        print(f"错误：主文件缺少secID列")
        print(f"可用列: {available_columns}")
        print(f"清理后的列名: {[clean_bom_from_text(col) for col in available_columns]}")
        return

    # 查找日期列
    date_column = find_column('涨停日期', available_columns)
    if not date_column:
        # 尝试查找包含"日期"的列
        for col in available_columns:
            if '日期' in clean_bom_from_text(col):
                date_column = col
                break

        if not date_column:
            print(f"错误：主文件缺少日期列")
            print(f"可用列: {available_columns}")
            return

    print(f"   ✓ 找到secID列: {secid_column}")
    print(f"   ✓ 找到日期列: {date_column}")
    print(f"   ✓ 主文件列名: {available_columns}")
    
    # 初始化统计信息
    stats = {
        'total_records': len(main_data),
        'matched_records': 0,
        'unmatched_records': 0,
        'missing_files': 0,
        'date_mismatches': 0,
        'total_trading_days_matched': 0
    }
    
    print(f"\n3. 开始数据合并过程...")
    print(f"   总共需要处理 {stats['total_records']} 条记录")
    
    # 准备合并后的数据
    merged_data = []
    
    # 遍历每行数据进行合并
    for index, row in enumerate(main_data):
        # 显示进度
        if (index + 1) % 10 == 0 or index == 0:
            progress = (index + 1) / len(main_data) * 100
            print(f"   处理进度: {progress:.1f}% ({index + 1}/{len(main_data)})")
        
        # 创建新行，复制原始数据
        merged_row = row.copy()
        
        # 提取股票代码（使用找到的secID列名）
        stock_code = extract_stock_code(row.get(secid_column, ''))
        if not stock_code:
            if index < 5:
                print(f"   警告：第 {index + 1} 行无法提取股票代码 from {row.get(secid_column, '')}")
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 查找匹配文件
        matching_file = find_matching_file(stock_code, data_source_dir)
        if not matching_file:
            if index < 5:
                print(f"   警告：第 {index + 1} 行未找到股票代码 {stock_code} 的资金流向文件")
            stats['missing_files'] += 1
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 标准化涨停日期（使用找到的日期列名）
        target_date = standardize_date_format(row.get(date_column, ''))
        if not target_date:
            if index < 5:
                print(f"   警告：第 {index + 1} 行无法解析涨停日期 {row.get(date_column, '')}")
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue
        
        # 读取并匹配资金流向数据
        fund_flow_data = read_csv_file(matching_file)
        if not fund_flow_data:
            if index < 5:
                print(f"   警告：第 {index + 1} 行无法读取文件 {matching_file}")
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue

        # 使用第一列作为日期列（通常是"日期"）
        fund_flow_columns = list(fund_flow_data[0].keys())
        fund_date_column = fund_flow_columns[0]

        # 查找涨停日期在资金流向数据中的位置
        target_row_index = -1
        for i, fund_row in enumerate(fund_flow_data):
            fund_date = standardize_date_format(fund_row.get(fund_date_column, ''))
            if fund_date == target_date:
                target_row_index = i
                break

        if target_row_index == -1:
            if index < 5:
                print(f"   警告：第 {index + 1} 行在文件中未找到涨停日期 {target_date} 的数据")
            stats['date_mismatches'] += 1
            stats['unmatched_records'] += 1
            merged_data.append(merged_row)
            continue

        # 获取涨停日及前5个交易日的数据（基于行索引）
        matched_days = 0

        # 处理涨停当天数据
        target_row = fund_flow_data[target_row_index]
        matched_days += 1

        # 添加涨停日数据（原始日期放在最前面）
        date_suffix = "涨停日"
        merged_row[f"资金流向_{date_suffix}_原始日期"] = target_date

        # 只保留包含"净占比"的字段
        for col, value in target_row.items():
            if col != fund_date_column and "净占比" in col:
                new_col_name = f"资金流向_{date_suffix}_{col}"
                merged_row[new_col_name] = value

        # 处理前5个交易日数据
        for day_offset in range(1, 6):  # 前1日到前5日
            prev_index = target_row_index - day_offset
            if prev_index >= 0:  # 确保索引有效
                prev_row = fund_flow_data[prev_index]
                prev_date = standardize_date_format(prev_row.get(fund_date_column, ''))

                if prev_date:  # 确保日期有效
                    matched_days += 1
                    date_suffix = f"前{day_offset}日"

                    # 添加原始日期（放在最前面）
                    merged_row[f"资金流向_{date_suffix}_原始日期"] = prev_date

                    # 只保留包含"净占比"的字段
                    for col, value in prev_row.items():
                        if col != fund_date_column and "净占比" in col:
                            new_col_name = f"资金流向_{date_suffix}_{col}"
                            merged_row[new_col_name] = value

        # 记录匹配统计
        stats['matched_records'] += 1
        # 添加匹配天数统计
        merged_row['资金流向_匹配天数'] = matched_days

        merged_data.append(merged_row)
    
    # 保存结果
    print(f"\n4. 保存合并结果...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"merged_data_{timestamp}.csv"
    
    # 获取所有字段名
    all_fieldnames = set()
    for row in merged_data:
        all_fieldnames.update(row.keys())
    all_fieldnames = sorted(list(all_fieldnames))
    
    if write_csv_file(output_file, merged_data, all_fieldnames):
        print(f"   ✓ 合并结果已保存到: {output_file}")
    else:
        print(f"   错误：保存文件失败")
        return
    
    # 计算总匹配交易日数
    total_trading_days_matched = 0
    for row in merged_data:
        if '资金流向_匹配天数' in row:
            try:
                total_trading_days_matched += int(row['资金流向_匹配天数'])
            except (ValueError, TypeError):
                pass

    # 打印统计信息
    print(f"\n5. 数据合并统计报告")
    print("="*60)
    print(f"总记录数: {stats['total_records']}")
    print(f"成功匹配: {stats['matched_records']}")
    print(f"未匹配记录: {stats['unmatched_records']}")
    print(f"缺失文件: {stats['missing_files']}")
    print(f"日期不匹配: {stats['date_mismatches']}")
    print(f"总匹配交易日数: {total_trading_days_matched}")

    if stats['total_records'] > 0:
        success_rate = (stats['matched_records'] / stats['total_records']) * 100
        print(f"匹配成功率: {success_rate:.2f}%")

        if stats['matched_records'] > 0:
            avg_days_per_record = total_trading_days_matched / stats['matched_records']
            print(f"平均每条记录匹配天数: {avg_days_per_record:.2f}")

    print(f"说明：每条涨停记录最多匹配6天数据（涨停当天+前5个交易日）")
    print(f"数据字段：只保留净占比相关字段，原始日期字段位于每个交易日数据最前面")
    
    print("="*60)
    print(f"程序执行完成！合并结果已保存到: {output_file}")
    
    # 显示合并后的数据示例
    print(f"\n6. 合并后数据示例（前3行）:")
    print("-" * 60)
    if merged_data:
        # 显示列名
        print("列名:")
        for i, col in enumerate(all_fieldnames):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\n前3行数据:")
        for i, row in enumerate(merged_data[:3]):
            print(f"\n第 {i+1} 行:")
            # 显示基本信息
            basic_columns = [
                (secid_column, 'secID'),
                ('secShortName', 'secShortName'),
                (date_column, '涨停日期'),
                ('资金流向_匹配天数', '匹配天数')
            ]

            for actual_col, display_name in basic_columns:
                if actual_col in row:
                    print(f"  {display_name}: {row[actual_col]}")

            # 显示各交易日的资金流向数据示例
            print(f"  资金流向数据示例:")
            fund_flow_columns = [col for col in row.keys() if col.startswith('资金流向_') and not col.endswith('_原始日期')]

            # 按日期分组显示
            day_groups = {}
            for col in fund_flow_columns:
                if '_涨停日_' in col:
                    day_key = '涨停日'
                elif '_前1日_' in col:
                    day_key = '前1日'
                elif '_前2日_' in col:
                    day_key = '前2日'
                elif '_前3日_' in col:
                    day_key = '前3日'
                elif '_前4日_' in col:
                    day_key = '前4日'
                elif '_前5日_' in col:
                    day_key = '前5日'
                else:
                    continue

                if day_key not in day_groups:
                    day_groups[day_key] = []
                day_groups[day_key].append(col)

            # 显示每个交易日的数据
            for day_key in ['涨停日', '前1日', '前2日', '前3日', '前4日', '前5日']:
                if day_key in day_groups and day_groups[day_key]:
                    # 显示该日期的原始日期
                    date_col = f"资金流向_{day_key}_原始日期"
                    if date_col in row:
                        print(f"    {day_key}({row[date_col]}):")
                    else:
                        print(f"    {day_key}:")

                    # 显示主要净占比指标
                    key_indicators = ['主力净流入-净占比', '超大单净流入-净占比', '大单净流入-净占比']
                    for indicator in key_indicators:
                        for col in day_groups[day_key]:
                            if indicator in col:
                                print(f"      {indicator}: {row[col]}")
                                break

if __name__ == "__main__":
    main()
